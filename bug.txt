-- Found Git: D:/soft/Espressif/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- Component directory D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib does not contain a CMakeLists.txt file. No component will be added
-- Component directory D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal does not contain a CMakeLists.txt file. No component will be added
-- Component directory D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib does not contain a CMakeLists.txt file. No component will be added
-- Component directory D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal does not contain a CMakeLists.txt file. No component will be added
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32
NOTICE: Processing 5 dependencies:
NOTICE: [1/5] espressif/bdc_motor (0.1.0)
NOTICE: [2/5] espressif/mdns (1.8.2)
NOTICE: [3/5] espressif/mpu6050 (1.2.0)
NOTICE: [4/5] espressif/pid_ctrl (0.1.1)
NOTICE: [5/5] idf (5.4.2)
-- Project sdkconfig file D:/Embedded_Systetm/Project/esp32/micu_ros_car/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: D:/soft/Espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "micu_ros_car" version: 1
-- Adding linker script D:/Embedded_Systetm/Project/esp32/micu_ros_car/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/Embedded_Systetm/Project/esp32/micu_ros_car/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.newlib-data.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.syscalls.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/esp32/ld/esp32.peripherals.ld
-- Components: UART WP_Math app_mpu6050 app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp32_i2c_rw esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__bdc_motor espressif__mdns espressif__mpu6050 espressif__pid_ctrl esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 imu_ahrs json log lwip main mbedtls mcu_dmp motor mqtt newlib nvs_flash nvs_sec_provider oled openthread partition_table perfmon proto_data protobuf-c protocomm pthread ring_buffer rotary_encoder rt sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi wifi_provisioning wireless_conn wpa_supplicant xtensa
-- Component paths: D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/UART D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/WP_Math D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/app_mpu6050 D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/app_trace D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/app_update D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader_support D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bt D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/cmock D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/console D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/cxx D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/efuse D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp-tls D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/esp32_i2c_rw D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_adc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_app_format D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_bootloader_format D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_coex D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_common D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ana_cmpr D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_cam D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_dac D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_gpio D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_gptimer D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_i2c D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_i2s D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_isp D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_jpeg D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ledc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_mcpwm D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_parlio D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_pcnt D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ppa D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_rmt D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdio D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdm D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdmmc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdspi D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_spi D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_touch_sens D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_tsens D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_uart D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_usb_serial_jtag D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_eth D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_event D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_gdbstub D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hid D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_http_client D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_http_server D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_https_ota D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_https_server D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_lcd D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_local_ctrl D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_mm D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_netif D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_netif_stack D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_partition D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_phy D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_pm D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_psram D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_ringbuf D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_security D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_system D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_timer D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_vfs_console D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_wifi D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/espcoredump D:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__bdc_motor D:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__mdns D:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__mpu6050 D:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__pid_ctrl D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esptool_py D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/fatfs D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/hal D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/heap D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/http_parser D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/idf_test D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/ieee802154 D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/imu_ahrs D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/json D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/log D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip D:/Embedded_Systetm/Project/esp32/micu_ros_car/main D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mqtt D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/newlib D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/nvs_flash D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/nvs_sec_provider D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/openthread D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/partition_table D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/perfmon D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/proto_data D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protobuf-c D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/pthread D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/ring_buffer D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/rotary_encoder D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/rt D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/sdmmc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/spi_flash D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/spiffs D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/tcp_transport D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/ulp D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/unity D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/usb D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/vfs D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wear_levelling D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wifi D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wifi_provisioning D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wireless_conn D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wpa_supplicant D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/xtensa
-- Configuring done (14.2s)
-- Generating done (2.3s)
-- Build files have been written to: D:/Embedded_Systetm/Project/esp32/micu_ros_car/build

 *  正在执行任务: D:\soft\Espressif\tools\ninja\1.12.1\ninja.EXE  

[4/1038] Generating ../../partition_table/partition-table.bin
Partition table binary generated. Contents:
*******************************************************************************
# ESP-IDF Partition Table
# Name, Type, SubType, Offset, Size, Flags
nvs,data,nvs,0x9000,24K,
phy_init,data,phy,0xf000,4K,
factory,app,factory,0x10000,1M,
*******************************************************************************
[882/1038] Building C object esp-idf/motor/CMakeFiles/__idf_motor.dir/src/controller.c.obj
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/src/controller.c: In function 'tb6612_init_gpio':
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/src/controller.c:178:19: warning: unused variable 'ret' [-Wunused-variable]
  178 |         esp_err_t ret = gpio_config(&io_conf);
      |                   ^~~
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/src/controller.c:199:19: warning: unused variable 'ret' [-Wunused-variable]
  199 |         esp_err_t ret = gpio_config(&io_conf);
      |                   ^~~
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/src/controller.c: At top level:
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/src/controller.c:129:18: warning: 'tb6612_brake' defined but not used [-Wunused-function]
  129 | static esp_err_t tb6612_brake(mc_handle_t handle) {
      |                  ^~~~~~~~~~~~
[921/1038] Building C object esp-idf/wireless_conn/CMakeFiles/__idf_wireless_conn.dir/wireless_conn.c.obj   
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wireless_conn/wireless_conn.c: In function 'ros_send_task':
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wireless_conn/wireless_conn.c:87:13: warning: unused variable 'temp' [-Wunused-variable]
   87 |     uint8_t temp = 6;
      |             ^~~~
[935/1038] Building C object esp-idf/oled/CMakeFiles/__idf_oled.dir/oled.c.obj
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled/oled.c: In function 'oled_ascii':
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled/oled.c:271:63: warning: iteration 6 invokes undefined behavior [-Waggressive-loop-optimizations]
  271 |       esp32_i2c_write_byte(OLED_ADDR, 0x40, ascii[str[i] - 32][j]);
      |                                             ~~~~~~~~~~~~~~~~~~^~~
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled/oled.c:270:23: note: within this loop        
  270 |     for (int j = 0; j < 8; j++) {
      |                     ~~^~~
[1008/1038] Building C object esp-idf/mcu_dmp/CMakeFiles/__idf_mcu_dmp.dir/mcu_dmp.c.obj
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c: In function 'ekf_update':  
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c:370:11: warning: unused variable 'F' [-Wunused-variable]
  370 |     float F[2][2] = {{1.0f, dt}, {0.0f, 1.0f}};
      |           ^
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c: In function 'imu_get_euler_angles':
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c:411:18: warning: variable 'last_raw_yaw' set but not used [-Wunused-but-set-variable]
  411 |     static float last_raw_yaw = 0.0f;
      |                  ^~~~~~~~~~~~
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c: At top level:
D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/mcu_dmp.c:103:14: warning: 'filtered_yaw_rate' defined but not used [-Wunused-variable]
  103 | static float filtered_yaw_rate = 0.0f;
      |              ^~~~~~~~~~~~~~~~~
[1015/1038] Building C object esp-idf/rotary_encoder...otary_encoder.dir/src/rotary_encoder_pcnt_ec11.c.obj 
In file included from D:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/rotary_encoder/src/rotary_encoder_pcnt_ec11.c:19:
D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/deprecated/driver/pcnt.h:15:2: warning: #warning "legacy pcnt driver is deprecated, please migrate to use driver/pulse_cnt.h" [-Wcpp]
   15 | #warning "legacy pcnt driver is deprecated, please migrate to use driver/pulse_cnt.h"
      |  ^~~~~~~
[1028/1038] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
D:\soft\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_MDNS_VERSION_NUMBER=\"1.8.2\" -DESP_PLATFORM -DIDF_VER=\"v5.4.2\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/build/config -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/main -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/newlib/platform_include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/config/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/config/include/freertos -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/config/xtensa/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/FreeRTOS-Kernel/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos/esp_additions/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/include/soc -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/include/soc/esp32 -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/dma/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/ldo/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/debug_probe/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/port/esp32/. -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support/port/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/heap/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/heap/tlsf -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/log/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/esp32 -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/esp32/register -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/hal/platform_port/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/hal/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/hal/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/include/esp32 -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32 -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_common/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_system/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_system/port/soc -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_system/port/include/private -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/xtensa/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/xtensa/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/xtensa/deprecated_include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/include/apps -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/include/apps/sntp -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/lwip/src/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/port/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/port/freertos/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/port/esp32xx/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/port/esp32xx/include/arch -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/lwip/port/esp32xx/include/sys -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__mdns/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/console -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/vfs/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_vfs_console/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_netif/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_event/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__mpu6050/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/deprecated -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/i2c/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/touch_sensor/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/twai/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/driver/touch_sensor/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_pm/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_ringbuf/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_gpio/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_pcnt/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_gptimer/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_spi/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_mcpwm/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ana_cmpr/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_i2s/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdmmc/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/sdmmc/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdspi/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdio/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_dac/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_rmt/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_tsens/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_sdm/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_i2c/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_uart/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ledc/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_parlio/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_usb_serial_jtag/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/port/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/mbedtls/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/mbedtls/library -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/esp_crt_bundle/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_app_format/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_bootloader_format/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/app_update/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader_support/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_partition/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/efuse/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/efuse/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_mm/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/spi_flash/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_security/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/pthread/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_timer/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/app_trace/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/nvs_flash/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_phy/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_phy/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wpa_supplicant/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wpa_supplicant/port/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wpa_supplicant/esp_supplicant/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_coex/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_wifi/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_wifi/include/local -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_wifi/wifi_apps/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_gdbstub/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/unity/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/unity/unity/src -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/cmock/CMock/src -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/http_parser -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp-tls -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp-tls/esp-tls-crypto -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_adc/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_adc/interface -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_adc/esp32/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_adc/deprecated/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_isp/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_cam/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_cam/interface -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_psram/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_jpeg/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_driver_ppa/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_eth/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hid/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/tcp_transport/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_http_client/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_http_server/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_https_ota/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_https_server/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_lcd/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_lcd/interface -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protobuf-c/protobuf-c -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm/include/common -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm/include/security -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm/include/transports -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm/include/crypto/srp6a -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/protocomm/proto-c -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_local_ctrl/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/espcoredump/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/espcoredump/include/port/xtensa -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wear_levelling/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/fatfs/diskio -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/fatfs/src -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/fatfs/vfs -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/idf_test/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/idf_test/include/esp32 -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/ieee802154/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/json/cJSON -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/mqtt/esp-mqtt/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/nvs_sec_provider/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/perfmon/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/rt/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/spiffs/include -ID:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/wifi_provisioning/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__pid_ctrl/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__bdc_motor/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/managed_components/espressif__bdc_motor/interface -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/motor/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/ring_buffer -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/ring_buffer/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/UART -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/UART/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wireless_conn -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wireless_conn/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/proto_data -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/protocal/proto_data/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/app_mpu6050/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/esp32_i2c_rw/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/oled/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/wifi/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/WP_Math -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/WP_Math/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/imu_ahrs -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/imu_ahrs/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/mcu_dmp/include -ID:/Embedded_Systetm/Project/esp32/micu_ros_car/components/lib/rotary_encoder/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=D:/Embedded_Systetm/Project/esp32/micu_ros_car=. -fmacro-prefix-map=D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -c D:/Embedded_Systetm/Project/esp32/micu_ros_car/main/main.c
D:/Embedded_Systetm/Project/esp32/micu_ros_car/main/main.c: In function 'mc_init':
D:/Embedded_Systetm/Project/esp32/micu_ros_car/main/main.c:120:38: error: 'GPIO_NUM_47' undeclared (first use in this function); did you mean 'GPIO_NUM_37'?
  120 |     config.tb6612_config.dir1_gpio = GPIO_NUM_47;       // 鏂瑰悜鎺у埗寮曡剼1 (IN1)
      |                                      ^~~~~~~~~~~
      |                                      GPIO_NUM_37
D:/Embedded_Systetm/Project/esp32/micu_ros_car/main/main.c:120:38: note: each undeclared identifier is reported only once for each function it appears in
D:/Embedded_Systetm/Project/esp32/micu_ros_car/main/main.c:121:38: error: 'GPIO_NUM_48' undeclared (first use in this function); did you mean 'GPIO_NUM_38'?
  121 |     config.tb6612_config.dir2_gpio = GPIO_NUM_48;       // 鏂瑰悜鎺у埗寮曡剼2 (IN2)
      |                                      ^~~~~~~~~~~
      |                                      GPIO_NUM_38
[1029/1038] Performing configure step for 'bootloader'
-- Found Git: D:/soft/Espressif/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/soft/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32
-- Project sdkconfig file D:/Embedded_Systetm/Project/esp32/micu_ros_car/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc/esp32/ld/esp32.peripherals.ld
-- Bootloader project name: "bootloader" version: 1
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader/subproject/main/ld/esp32/bootloader.ld
-- Adding linker script D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader/subproject/main/ld/esp32/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_bootloader_format esp_common esp_hw_support esp_rom esp_security esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader_support D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/efuse D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_app_format D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_bootloader_format D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_common D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_hw_support D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_rom D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_security D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esp_system D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/esptool_py D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/freertos D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/hal D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/log D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader/subproject/main D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/bootloader/subproject/components/micro-ecc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/newlib D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/partition_table D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/soc D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/spi_flash D:/Embedded_Systetm/Project_Tool_Backup/ESP32/BackUp/v5.4.2/esp-idf/components/xtensa
-- Configuring done (27.2s)
-- Generating done (0.3s)
-- Build files have been written to: D:/Embedded_Systetm/Project/esp32/micu_ros_car/build/bootloader        
ninja: build stopped: subcommand failed.