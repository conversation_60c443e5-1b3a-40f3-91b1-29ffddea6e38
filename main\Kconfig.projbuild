menu "micro-ROS example-app settings"

    config MICRO_ROS_APP_STACK
        int "Stack the micro-ROS app (Bytes)"
        default 16000
        help
        Stack size in Bytes of the micro-ROS app

    config MICRO_ROS_APP_TASK_PRIO
        int "Priority of the micro-ROS app"
        default 5
        help
        Priority of micro-ros task higher value means higher priority


    config MICRO_ROS_DOMAIN_ID
        int "Ros domain id of the micro-ROS"
        default 20
        range 0 101
        help
        To avoid interference between different groups of computers running ROS 2 on the same network, set a different domain ID value for each computer group.
        
    config MICRO_ROS_NAMESPACE
        string "Ros namespace of the micro-ROS"
        default ""
        help
        The ROS node namespace
        
endmenu