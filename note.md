代码要点简述：

WiFi以STA模式运行（wifi.c里定义了STA_MODE），开机会连接你配置的路由器SSID/密码，并启动mDNS（micu-ros-car.local）。
ESP32作为网络服务端，绑定本机端口：TCP 8080（配置管理），UDP 8082（底盘双向），UDP 8083（雷达单向）。上位机需先通过TCP 8080发送一段JSON，告诉ESP32你Linux的IP与端口，ESP32才会把UDP数据发给你的Linux。
烧录完后的步骤（按桥接模式，最稳妥）：

虚拟机网络设置
在VirtualBox/VMware把虚拟机网络调为“桥接模式”，桥接到Windows当前使用的网卡（Wi‑Fi或有线），这样Linux与ESP32能拿到同一网段IP。
确认ESP32的WiFi连接
确保在代码中设置了路由器的SSID/密码（在 components/wifi/wifi.h 的 DEFAULT_WIFI_SSID / DEFAULT_WIFI_PASSWORD）。
重新烧录并复位ESP32，打开串口日志（115200），看到类似“WiFi connected successfully! IP: 192.168.1.xx”；OLED也会显示“HOST:IP”。
获取Linux虚拟机IP并互相连通
在Linux执行：ip addr，记录虚拟机的IPv4（如*************）。
在Linux ping ESP32：ping ESP32_IP；能通则网络OK。也可用 mDNS：ping micu-ros-car.local（虚拟机需支持avahi）。
用TCP 8080配置UDP目标地址（必做）
在Linux终端用nc向ESP32发送JSON，分别配置底盘与雷达目标：
底盘（UDP 8082）：echo '{"ip":"LINUX_IP","port":8082,"type":"chassis"}' | nc -v ESP32_IP 8080
雷达（UDP 8083）：echo '{"ip":"LINUX_IP","port":8083,"type":"lidar"}' | nc -v ESP32_IP 8080
成功后ESP32会回 {"port":8082} 或 {"port":8083}，串口日志出现“Lidar target address set to …”等字样。
在Linux打开UDP接收并测试通信
接收底盘状态：nc -u -l -p 8082（可看到ESP32周期发的状态帧）。
接收雷达数据：nc -u -l -p 8083（DMA转发的激光雷达数据流）。
发送控制指令到ESP32：nc -u ESP32_IP 8082（需按你协议格式发送合法数据帧，否则ESP32会丢弃）。
防火墙放行（避免端口被拦截）
Linux：sudo ufw allow 8080/tcp; sudo ufw allow 8082/udp; sudo ufw allow 8083/udp
路由器务必关闭“客户端隔离”。
如果虚拟机用NAT而非桥接：

从虚拟机访问ESP32无需改动，直接用ESP32_IP。
让ESP32把UDP发到虚拟机：在虚拟机软件的NAT端口转发里把主机的8082/8083映射到客机Linux的8082/8083；然后在第4步的JSON里，把"ip"写成宿主机的IP，"port"写为映射后的“主机端口”。
完成以上步骤后，ESP32与Linux即可在同网段双向通信，无需改业务代码；只需确保SSID/密码正确并完成一次TCP 8080的目标配置。